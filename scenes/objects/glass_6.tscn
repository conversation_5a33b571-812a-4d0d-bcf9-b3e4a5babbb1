[gd_scene load_steps=4 format=3 uid="uid://bw6ee1jlymmpn"]

[ext_resource type="Texture2D" uid="uid://be82t8d7ajb5t" path="res://images/objects/glasses/1163A0.png" id="1_843h3"]
[ext_resource type="Script" uid="uid://0lax4sx4ont5" path="res://scenes/objects/break_glass.gd" id="1_pidia"]

[sub_resource type="BoxShape3D" id="BoxShape3D_gkagf"]
size = Vector3(0.107422, 0.0605469, 0.124023)

[node name="Glass6" type="StaticBody3D"]
collision_layer = 4
collision_mask = 7
script = ExtResource("1_pidia")

[node name="Sprite3D" type="Sprite3D" parent="."]
billboard = 2
shaded = true
texture_filter = 2
render_priority = 2
texture = ExtResource("1_843h3")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_gkagf")
