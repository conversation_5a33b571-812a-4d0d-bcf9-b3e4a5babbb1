[gd_scene load_steps=38 format=4 uid="uid://b4l0xb0padsci"]

[ext_resource type="Script" uid="uid://d2g5jvjq61kgj" path="res://scenes/objects/Door.gd" id="1_nhaao"]
[ext_resource type="AudioStream" uid="uid://bryw155gw6q1g" path="res://sounds/sfx/DSSTNMOV.wav" id="2_oeqx6"]

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_eab58"]
_data = PackedByteArray("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")
size_override = Vector2(128, 128)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_kbiir"]
resource_name = "PillarSide"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_eab58")
roughness = 0.9

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_oeqx6"]
_data = PackedByteArray("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")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_npnwq"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_oeqx6")
roughness = 0.5
uv1_offset = Vector3(0.45, 0.045, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_33kgk"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_fygvl"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_npnwq"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_33kgk")

[sub_resource type="BoxShape3D" id="BoxShape3D_a4yma"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_nhaao"]
_data = PackedByteArray("AgAEAAQAAAAHAAAAQAAAAEAAAAABAAAAc0ITAE0AfDURFgAAF2AHAAABAAABEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABNAAAAAAAAAAAAAAAAAAAAAEAAQAAQABAA7gAAAAAQAACZFgAAAAEAIAAgAAgACADuEAAAAAQAAK5uAAAAAgAQABAABAAEAO4UAAAAAQAAIlgAAAADAAgACAACAAIA7hUAAEAAAABeTgAAAAQABAAEAAEAAQAuFgAAEAAAAJyRAAAABQACAAIAAQABAD4WAAAQAAAAm1wAAAAGAAEAAQABAAEAThYAABAAAAAH9YFAk1eFVViFf3EXcR8RH/+BQJOXhVmYhfX/IiIi8vL/gUCTl2ZplmbvcO5+7n53d4FAk1eFVViFExEXcXd3H3+BQJNXhVVYhR8Rf3d/d3d3gUCTV4VVWIUf/3F3f3d/94FAk5dVWZVVAQCwu7u7uwCBSaMOBmEQBgEzVVO+VVNbgQiQl4RJmIQTEbuIu/i7+4FAk5eESZiEg/+4u4uIi4gBQJMXJmESJkF05Ed3d3dHAUGTFyZhEiZBRETnd3dEd8FAkxcmYRImSXBEfgR+AEQBQJMXJmESJn/n5+53d0BEQUCTFyZhEiZPdO53d05EBMFBy4smaZImj3joiId3MDPBSJOXJVmSJQEAMzMzMzM+gUCTl2ZplmYPAA7n7nfu7sEQkBdWYRVWAQAOiIiIiIiBQJNXpVVapUmk9KoR9EoRgUCTl2Zplmbvfnd3cHd+7oFAk5dWaZVWsVVVVbWw6+uBQJNXhVVYhXN393939x9xgUCTV4VVWIV/d///H//3d8FAk5elWZqlE/iIiI+I+IjBQZMXJmESJnl+cHdABAQEgUCTV4VVWIVzEfF3EXcR8YFAkxdWYRVWgYgAgOiOiIiBQJOXpVmapYMYgYgRiBH4QUnjUdVVXdV3ev/6d3c3ccFAk5dWaZVWWwVVu7u7vrvBSJOXJmmSJjFzMAdzd/ePwUCTV6VVWqUTEapBRBREr4FAkxdWYRVWgQgA4O4IiACBSLuUJmmSJn8HMzMzlzOfgUiTFyZhEiZxd3TuRwREAMFAk9eETdiEr3cXd/+ven/BAZCXJVmSJQc+MwAAAAAAQUijTvVVX/UrIFgiVYjyiIFAk1elVVqlSUSqREqh/6rBQJOXpEmapAGHiHeI+MyIgUCTl6VZmqWDGBGIiBgR8YFAk1eFVViFf/f/d3EXFxeBQJMXVmEVVgGIiAjuAO6IgUCTV6VVWqWppEShofpKpMFAk1eFVViF///3/xd/cXfBSLuUJmmSJjd5c3d3MPl3AUGTV6VVWqUZEUQRFET6REFAkxcmYRImcUTk7nTuQHSBQZOXJVmSJTcA7jPu7gMwgUCjjqRJmqRtz2bMAMZmiIFAkxcmYRImQUAARABERH7BQJMXVmEVVgEAAAAOiIjogQK4lCZpkiZ3d3c3BzN/NwFIk5fVWZ3VUVlVVVVfWVWBAJAXZmEWZgEA3dDQDQAAgUCTl1ZplVZb5VDlu1W+u4FAkxdWYRVWAeCACIiIiABBQJOXJmmSJjcwdwN3/3d3gUCTl1ZplVZbtVVbvlvuC8FAu5QmaZImf/dzd5dzMDOBQJNXhVVYhRMRcXcRf3f/gUCTV4VVWIUTcRF3d3d/d0FJk1elVVql+UQRRBFERErBQJNXhVVYhX93d3cfd3f3wUCTF1ZhFVaB6IiIgAgOAIFIy4smaZImd4hziHDoM4iBQJNXhVVYhRMRERERFxF/gUCTV4VVWIUTEXcX8Xf3/4FAk1eFVViFE/dxd3d3d/eBQJOXJmmSJn+HdzePB4g3AUnjUdVVXdVzp/eveqd3d4FBk1fVVV3VPzM/MzcwBwCBQJOXpVmapfMY+P+BiBGBgQCQV4VVWIXzd/////939wFBk1elVVql+apEpBpKRESBQJMXZmEWZgEA0A3d3dDdgUCTFzZhEzYBgI6IiFhVhcFAk5cmaZImBzN3h4j/h4iBQJOXZmmWZgHnAHdwfufuwUG7lCZpkiYPM383mTd5d4FAu5QmaZImMTMzMzd3l/nBQZMXJmESJkFERO4ER3cEgUiTV/VVX/UBUgBVAvJVUoFAk5dWaZVWW14AtQAAUFUBQZOXVmmVVlFVXrtVVVVVgUCTl4VZmIUlLyIi///yL4FAk1elVVql+UoUpESkpEQBQZNXpVVapRNBREGvREQRgUCTV4VVWIV/d/f///93cYFAk5elWZqlE4EYj4gYiBGBSZOXJVmSJTEDMzPjPjMDgUCTFzZhEzZRVYWIjoiFUIFAk1eFVViFf/d393F393+BQJNXhVVYhX//d3d/dxERgUCTV4VVWIXzd/d3d38XEYFAk5elWZqlExERgY8YiIiBQJMXJmESJnnnRARAAEQAgUCTV4VVWIUTcRF3/3//d4FAkxdWYRVWAQCIiIiACA6BQJOXZmmWZn/ufud+4Hd3gUiTl1ZplVa7UO5V6+7l7oFAk1elVVqlSapE+hH/Ea/BEZBXJ3VSJ3/5p6lw93eZAUGTF1RBFVQfd3f3d/d//wFAk5clWZIlNzPuMzMzMDOBAZCXJVmSJQEA4w4zAAMAgUiTlwVZkAUhIpIpkJmSLoEAkFeFVViFf3d3/3F/d//BQJMXJmESJnlEQEQEcADkgUGTlyZpkiaPiDB3d4j//4FAk1eFVViFH3cf8Xf38f/BQJNXhVVYhfP/d3cRERERgUCTFyZhEiZJB35Ed3d3RIFIo44maZImfweXfpmZl3mBQJOXVmmVVltQW1VbW7vrwQiQFyZhEiYBQABERATnd4FAkxcmYRImcURA50REB0CBQJOXpVmapYOIEYiPiBEYgQCQF2ZhFmYB0ADQAAAAAMFAk1elVVqlExEUEa+vRKqBQJNXhVVYhX93d/939xd3wUCTFyZhEiZJAEQAQERE5IFAkxcmYRImeXdAQHRHfkTBQLtU1VVd1W1q+q9jZmYTAUmTV9VVXdUPA383NzMHM4EIkJeESZiEExGLuLu/u7tBSJOXZEmWZFPMz8z/////gUCTlyVZkiXnMzMzADPuM4FAk1elVVqlGaFKpKSqqv+BQJNXpVVapUlEqkShr0qhAUiTV6VVWqVJFKREqkRE9MEBkBcmYRIm73cEdwBEAEDBQJNXhVVYhfP/8XH3d3EXwUCTV4VVWIVz93d3d/cR/4FAk1elVVqlSfpEpEShofrBAZCXhEmYhBP/i7+7u7uIwUCTV4VVWIUfd3f3//d3d4FAk1elVVqlSaSh+qqqRESBQJOXVmmVVlG1VVVeVbu7gUCTFyZhEiZBAEQARAd+RMEQuJQmaZImN5Mzd3cHl3+BQJNXpVVapUlKpKGqr6SqgUCTl4VZmIUlIv///y//IoFAk1eFVViFcxf3/3H/d3dBQZOX9Vmf9fF3QEdERABAgUCTl1ZplVYBVVVVu7W17sFAk5dWaZVWW7tV5VDlVbUBCZBXpVVapUn0qhpKpEpEgUCTV6VVWqUZFKqvRPoREYFAk1eFVViFE3f/f//3cf8BQJMXJmESJkEERORARADkgUG7FAZhEAZXVVNVtTP1MMFBuxQGYRAGOzNV9VVVMwPBQZMXJmESJu93fndHd0B0gUmTVyVVUiUBAAAA4AAAAIFAk1eFVViFExERdxfxf/eBQJNXhVVYhRMRF3F3d/93wUCTV4VVWIX/93cXdxf3d8EJkBcmYRImSQTnd+d3BHdBSKOOBmmQBn23tnd3Bm4zARmQV/VVX/UhUiL1VVVYWMFBk5dWaZVWUe7l7uXuu7uBAriUJmmSJn83nzeXN3AzQUiTl/VZn/VJAHQEf0d0R4FIkxcmYRImeXdwfkBHdHfBQJNXhVVYhf9///93dxd/gUCTV6VVWqWpFESkFKT0SoFAk1eFVViFf3//d3dxF3GBQJOXhVmYhfX////y/yIigUCTl6VZmqWDgYgYGI8RgQFAk5clWZIlATPj7jMzMAOBQJMXNmETNgsA7oiFiFBVQUHLi6RJmqTPz4eIeBeHd4FJkxcmYRImAUTnfO5+R3fBAeBR1VVd1X8z//p3dzdxAUiTV6VVWqVDqqSq9KpKoQFIk5dWaZVWW+VV61W7uwuBQJOXpVmapfMYERGBgRgRgUCTF2ZhFmYBAADQDdDd3QFJk5dWaZVWu1vrvuu+u7CBQJMXJmESJnl3ROQEQEREgUCTl6RJmqR/yHD4iMiIeAFJ41HVVV3Vf3d6p/evcaeBQZNX1VVd1Q8ANzA/MzczgUCTl6VZmqUTgYGI+P/xGIEAkFeFVViFf/f/////8XcBQZNXpVVapUlEGkpEpPSqgUCTF2ZhFmbR3d3d0A0AAIFAk5dWaZVWu1u1u7BbVV7BQJMXVmEVVoHoiIiACA4AwUCTV4VVWIX///f/F39xd4FIkxcmYRIm4e5E53d3RHeBQJMXJmESJn/nRABHQEREgUCTFyZhEiZ/RwBAQER3fsFAk1eFVViFf3f3F393Ef+BQZOXJmmSJjFwfzcHM3c3gUCTV6VVWqWpqkr6SqoURAFIk1dndVZnraqqoKqqr6aBAJAXZmEWZgEA0A3d0AAAgUCTV6VVWqVDRESqrxqqGoFAk1eFVViFf/93d3/3/x9BQJOXJmmSJn93d/93AzMwgUCTV6VVWqUT9EGkqqSqSsFAu5QmaZImMTOXc3N3d/fBQJOXJmmSJnd3dzCHeI93AUGTl1ZplVZRtVVVXlXuu8FAk1eFVViFExFxdxF/93+BQJOXVmmVVru7VbXlBVDrgUCTl4VZmIUlIiIv/y8vIsFAy4ukSZqkf3GIfMiMz8zBQJNXhVVYhRP/ER93fxd3gUCTF1ZhFVbhiIiIiIjuDoFAk5dmaZZm7+5+d3cHd3dBQaNO9VVf9fWIVYhYIiUggUCTl1ZplVYBVbVeVbu7u8FAk5cmaZImN3d3B3eI/3iBQJOXpVmapRPxiBgRiIEYgUCTV4VVWIUfF3EX/3fxcYFAk1eFVViFE3cR/3f3d3eBQJOXVmmVVrtbXgW7XrtbgUCTl2ZplmZx53d37ufu7gFAk5eESZiEE/uL/7v7u7uBQJMXZmEWZgENAAAA0N3dgUCTl1ZplVYBUFVVtVtV64FAk5dWaZVWW7VVVQtVXlWBQJNXhVVYhX938f/xd/d3gUCTl1ZplVaxvl4FtVvr7oFBk1elVVqlE0Gqqq9KEaSBQJNXhVVYhf//9/9/d38XgUCTF1ZhFVbhjogICICOiIFAk1eFVViF/3cf////d3eBQJOXVmmVVgtVUFXlVbu7wUCTFyZhEiYJBEAEQHTkR0FAkxcmYRImAQAAQEBEROcBSZNXpVVapUOkRBqv+kSqgUCTl6VZmqUT+IiIiIiIGIFAk1elVVqlSaqqGqT6RKqBQJOXpVmapRMRiIGI+IHxgUCTV6VVWqWpRKqq/6pKoUFJk1f1VV/1iwVfAFUAJSKBQZOXpEmapI+IB4eHh/jIQUi7lPVZn/VJFERH9HdEd4FIkxcmYRImSXcE5wB0QHfBQJNXhVVYhXP/8f93d3HxwUCTV6VVWqUZEaoU9ESkqoFAk1dlVVZlSf9ERE9E//+BSaMOBmEQBldbvlVVUwAzgUmTVyVVUiUBAAAAAA4AAIFAk1eFVViFExERcXER8XeBQJNXhVVYhRMRdxF/d/9/gUCTV4VVWIVzf3d3d3d3f4FIkxcmYRImQUR3DndORECBAJAXZmEWZgHQAAAAAN3dgUCTFyZhEiZBRHBE5EBEAIFAkxdWYRVWAYiIiAiI4ADBQPNIlVVZlV2oiMhV/wOngUCzBWZhFmYP/xR5QxSqekFAu5SFWZiFC7BHe+7/v5WBQJPXNV3TNVlBV50SACIAwUi7FJVRGZUFo5hW6ijfqoFAo06FVViFSQoxFEevgWaBSJPXpE3apF8DSmep/8eawUCTF5VRGZVRUmarvb92NoFAywtGYRRGiTAWB9h/5e6BQMtLRmVURhn3wTgKY3+0gUCTV4VVWIURA6p3/5lY54FAk5dmaZZm/+oClViKJYiBQKMOlVEZlVPJqsqfrKeowUCTV0ZlVEZ7dZlbBVLoSoFAkxdWYRVWXQQFa+a8EhuBQLsUVmEVVpHpD4GehYVbgUCjTpVVWZVbR3a4td9yBoFAk1dGZVRGu7vSe+dLeAaBQPOIpVmapQNQZlNtad+qwUEDQLVVW7Vb+4C7ZXeIZsFA20JmZVZmswD4Se555IfBQLMFVmEVVjUTE1YFqlDvgUC7FFZhFVYBmIW7tziPV4FAo45GaZRGe1D7hHmXqZmBQKNOlVVZlXskv9eWYBExwUCjTpVVWZVDmX2HVPsiZ8FAu1RWZVVWiQok6oL3r4mBQKNOpVVapckI8T6mipmqwUDLi5RJmZQxhWOIiMr9ucFAk5eESZiEj2e5Ffu7jKeBQAOAtVmbtWUjcgf+OlclgUDLC0ZhFEZzVv0F7p1GZgFAswWlURqlM4cx6of+lqlBQJOXlVmZlR8EpXe/OxRegUCTV4VVWIUXqZ9Y29qbtIFAk1eFVViFm/fk+JlJ6h7BQKPOhE3YhPfKms6JWMGpgUDzSEZlVEYdMERGr6qWrIFAkxdWYRVWDXB/ir7MV4RBQKNOlVVZlY+Z+31nUEcSgUCzBaVRGqUX9sHdx3yqmYFAkxdWYRVW4U+3FWQQZGYBQLPFVm3VVpv7QNqq65uZwUgDwFZt1VZ/qZuIf0SqBIFA20JmZVZmnyTtefhJsQDBQKMORmEURjP6BIgTRTITgUDjUYVVWIWn6GkGiqzuv8FBow61URu1O78QiFenh3TBAJAXNmETNv9nh0ZBRRBBwUCTF1ZhFVZ9pvhI6y4QaoFAy4uVWZmVq/iohDSIQP2BQJOXZmmWZiuIWIoClffqgUCjDpVRGZWvqJ+sqspRyYFAkxeVURmVH7X6rWaka4eBQMtLVmVVVktH168nQHl5gUC7VEZlVEYxmlhXPzJnZYFA88hWbdVWR3anZ8DPx6mBQONRpVVapY2IV1aqB/85gUCzBWZhFmZvBETECvacQYFAk9c1XdM1JQASAFedVEHBQLNFVmVVVjFmaLvMvPy5gUC7VFZlVVZFe/gE7IuEioFAy0tmZVZmD2dSCvxS/3fBQLtUVmVVVo1kBFCnpk5nwUDbAlZhFVYvgQs0NoL9SIFAo06VVVmVexOrljSuib+BQJNXhVVYhUNDb5XFmKu5gUCTV5VVWZUJUfmIiFdXZMFAAwBmYRZmP/kXG1A32w2BQJOXlVmZlRfxrldJWBOpgUCTFzZhEzYJMTWRn8dEZMFAu1RWZVVWvSuLmZiOYN7BQNtClVVZlcGK+Mzjh6u5AUG7lJVZmZVJdGdGD2YU8oFIk1eFVViFm9sxAdgN+a2BQKNOlVVZlX+qVSZE8JmIwUCTV5VVWZVRZYlnu4dJX8FAu1RWZVVWW0A6UWdpeOzBQLsUVmEVVkc1GXhwFL93gUCTl4VZmIUtLMrCz+7ghIFAu5SFWZiFf0lQ9UfMdccBQJPXVV3VVXcwEDEQE72QAUCTF2ZhFmY/mXU5uwVT10FAk5d1WZd1QcjP/M//9P8BQJPXZV3WZQ83B3sANwADAUCzxXVd13X//3B3cHdwd7erq+sfLqUAAAAAAAAAAAA=")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_oeqx6"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_nhaao")
roughness = 0.5
uv1_offset = Vector3(0.645, 0.67, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_npnwq"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_kpp4c"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_oeqx6"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_npnwq")

[sub_resource type="BoxShape3D" id="BoxShape3D_oeqx6"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_kpp4c"]
_data = PackedByteArray("AgAEAAQAAAAHAAAAQAAAAEAAAAABAAAAc0ITAE0AfDURFgAAF2AHAAABAAABEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABNAAAAAAAAAAAAAAAAAAAAAEAAQAAQABAA7gAAAAAQAACZFgAAAAEAIAAgAAgACADuEAAAAAQAAK5uAAAAAgAQABAABAAEAO4UAAAAAQAAIlgAAAADAAgACAACAAIA7hUAAEAAAABeTgAAAAQABAAEAAEAAQAuFgAAEAAAAJyRAAAABQACAAIAAQABAD4WAAAQAAAAm1wAAAAGAAEAAQABAAEAThYAABAAAAAH9YFAk1eFVViFf3EXcR8RH/+BQJOXhVmYhfX/IiIi8vL/gUCTl2ZplmbvcO5+7n53d4FAk1eFVViFExEXcXd3H3+BQJNXhVVYhR8Rf3d/d3d3gUCTV4VVWIUf/3F3f3d/94FAk5dVWZVVAQCwu7u7uwCBSaMOBmEQBgEzVVO+VVNbgQiQl4RJmIQTEbuIu/i7+4FAk5eESZiEg/+4u4uIi4gBQJMXJmESJkF05Ed3d3dHAUGTFyZhEiZBRETnd3dEd8FAkxcmYRImSXBEfgR+AEQBQJMXJmESJn/n5+53d0BEQUCTFyZhEiZPdO53d05EBMFBy4smaZImj3joiId3MDPBSJOXJVmSJQEAMzMzMzM+gUCTl2ZplmYPAA7n7nfu7sEQkBdWYRVWAQAOiIiIiIiBQJNXpVVapUmk9KoR9EoRgUCTl2Zplmbvfnd3cHd+7oFAk5dWaZVWsVVVVbWw6+uBQJNXhVVYhXN393939x9xgUCTV4VVWIV/d///H//3d8FAk5elWZqlE/iIiI+I+IjBQZMXJmESJnl+cHdABAQEgUCTV4VVWIVzEfF3EXcR8YFAkxdWYRVWgYgAgOiOiIiBQJOXpVmapYMYgYgRiBH4QUnjUdVVXdV3ev/6d3c3ccFAk5dWaZVWWwVVu7u7vrvBSJOXJmmSJjFzMAdzd/ePwUCTV6VVWqUTEapBRBREr4FAkxdWYRVWgQgA4O4IiACBSLuUJmmSJn8HMzMzlzOfgUiTFyZhEiZxd3TuRwREAMFAk9eETdiEr3cXd/+ven/BAZCXJVmSJQc+MwAAAAAAQUijTvVVX/UrIFgiVYjyiIFAk1elVVqlSUSqREqh/6rBQJOXpEmapAGHiHeI+MyIgUCTl6VZmqWDGBGIiBgR8YFAk1eFVViFf/f/d3EXFxeBQJMXVmEVVgGIiAjuAO6IgUCTV6VVWqWppEShofpKpMFAk1eFVViF///3/xd/cXfBSLuUJmmSJjd5c3d3MPl3AUGTV6VVWqUZEUQRFET6REFAkxcmYRImcUTk7nTuQHSBQZOXJVmSJTcA7jPu7gMwgUCjjqRJmqRtz2bMAMZmiIFAkxcmYRImQUAARABERH7BQJMXVmEVVgEAAAAOiIjogQK4lCZpkiZ3d3c3BzN/NwFIk5fVWZ3VUVlVVVVfWVWBAJAXZmEWZgEA3dDQDQAAgUCTl1ZplVZb5VDlu1W+u4FAkxdWYRVWAeCACIiIiABBQJOXJmmSJjcwdwN3/3d3gUCTl1ZplVZbtVVbvlvuC8FAu5QmaZImf/dzd5dzMDOBQJNXhVVYhRMRcXcRf3f/gUCTV4VVWIUTcRF3d3d/d0FJk1elVVql+UQRRBFERErBQJNXhVVYhX93d3cfd3f3wUCTF1ZhFVaB6IiIgAgOAIFIy4smaZImd4hziHDoM4iBQJNXhVVYhRMRERERFxF/gUCTV4VVWIUTEXcX8Xf3/4FAk1eFVViFE/dxd3d3d/eBQJOXJmmSJn+HdzePB4g3AUnjUdVVXdVzp/eveqd3d4FBk1fVVV3VPzM/MzcwBwCBQJOXpVmapfMY+P+BiBGBgQCQV4VVWIXzd/////939wFBk1elVVql+apEpBpKRESBQJMXZmEWZgEA0A3d3dDdgUCTFzZhEzYBgI6IiFhVhcFAk5cmaZImBzN3h4j/h4iBQJOXZmmWZgHnAHdwfufuwUG7lCZpkiYPM383mTd5d4FAu5QmaZImMTMzMzd3l/nBQZMXJmESJkFERO4ER3cEgUiTV/VVX/UBUgBVAvJVUoFAk5dWaZVWW14AtQAAUFUBQZOXVmmVVlFVXrtVVVVVgUCTl4VZmIUlLyIi///yL4FAk1elVVql+UoUpESkpEQBQZNXpVVapRNBREGvREQRgUCTV4VVWIV/d/f///93cYFAk5elWZqlE4EYj4gYiBGBSZOXJVmSJTEDMzPjPjMDgUCTFzZhEzZRVYWIjoiFUIFAk1eFVViFf/d393F393+BQJNXhVVYhX//d3d/dxERgUCTV4VVWIXzd/d3d38XEYFAk5elWZqlExERgY8YiIiBQJMXJmESJnnnRARAAEQAgUCTV4VVWIUTcRF3/3//d4FAkxdWYRVWAQCIiIiACA6BQJOXZmmWZn/ufud+4Hd3gUiTl1ZplVa7UO5V6+7l7oFAk1elVVqlSapE+hH/Ea/BEZBXJ3VSJ3/5p6lw93eZAUGTF1RBFVQfd3f3d/d//wFAk5clWZIlNzPuMzMzMDOBAZCXJVmSJQEA4w4zAAMAgUiTlwVZkAUhIpIpkJmSLoEAkFeFVViFf3d3/3F/d//BQJMXJmESJnlEQEQEcADkgUGTlyZpkiaPiDB3d4j//4FAk1eFVViFH3cf8Xf38f/BQJNXhVVYhfP/d3cRERERgUCTFyZhEiZJB35Ed3d3RIFIo44maZImfweXfpmZl3mBQJOXVmmVVltQW1VbW7vrwQiQFyZhEiYBQABERATnd4FAkxcmYRImcURA50REB0CBQJOXpVmapYOIEYiPiBEYgQCQF2ZhFmYB0ADQAAAAAMFAk1elVVqlExEUEa+vRKqBQJNXhVVYhX93d/939xd3wUCTFyZhEiZJAEQAQERE5IFAkxcmYRImeXdAQHRHfkTBQLtU1VVd1W1q+q9jZmYTAUmTV9VVXdUPA383NzMHM4EIkJeESZiEExGLuLu/u7tBSJOXZEmWZFPMz8z/////gUCTlyVZkiXnMzMzADPuM4FAk1elVVqlGaFKpKSqqv+BQJNXpVVapUlEqkShr0qhAUiTV6VVWqVJFKREqkRE9MEBkBcmYRIm73cEdwBEAEDBQJNXhVVYhfP/8XH3d3EXwUCTV4VVWIVz93d3d/cR/4FAk1elVVqlSfpEpEShofrBAZCXhEmYhBP/i7+7u7uIwUCTV4VVWIUfd3f3//d3d4FAk1elVVqlSaSh+qqqRESBQJOXVmmVVlG1VVVeVbu7gUCTFyZhEiZBAEQARAd+RMEQuJQmaZImN5Mzd3cHl3+BQJNXpVVapUlKpKGqr6SqgUCTl4VZmIUlIv///y//IoFAk1eFVViFcxf3/3H/d3dBQZOX9Vmf9fF3QEdERABAgUCTl1ZplVYBVVVVu7W17sFAk5dWaZVWW7tV5VDlVbUBCZBXpVVapUn0qhpKpEpEgUCTV6VVWqUZFKqvRPoREYFAk1eFVViFE3f/f//3cf8BQJMXJmESJkEERORARADkgUG7FAZhEAZXVVNVtTP1MMFBuxQGYRAGOzNV9VVVMwPBQZMXJmESJu93fndHd0B0gUmTVyVVUiUBAAAA4AAAAIFAk1eFVViFExERdxfxf/eBQJNXhVVYhRMRF3F3d/93wUCTV4VVWIX/93cXdxf3d8EJkBcmYRImSQTnd+d3BHdBSKOOBmmQBn23tnd3Bm4zARmQV/VVX/UhUiL1VVVYWMFBk5dWaZVWUe7l7uXuu7uBAriUJmmSJn83nzeXN3AzQUiTl/VZn/VJAHQEf0d0R4FIkxcmYRImeXdwfkBHdHfBQJNXhVVYhf9///93dxd/gUCTV6VVWqWpFESkFKT0SoFAk1eFVViFf3//d3dxF3GBQJOXhVmYhfX////y/yIigUCTl6VZmqWDgYgYGI8RgQFAk5clWZIlATPj7jMzMAOBQJMXNmETNgsA7oiFiFBVQUHLi6RJmqTPz4eIeBeHd4FJkxcmYRImAUTnfO5+R3fBAeBR1VVd1X8z//p3dzdxAUiTV6VVWqVDqqSq9KpKoQFIk5dWaZVWW+VV61W7uwuBQJOXpVmapfMYERGBgRgRgUCTF2ZhFmYBAADQDdDd3QFJk5dWaZVWu1vrvuu+u7CBQJMXJmESJnl3ROQEQEREgUCTl6RJmqR/yHD4iMiIeAFJ41HVVV3Vf3d6p/evcaeBQZNX1VVd1Q8ANzA/MzczgUCTl6VZmqUTgYGI+P/xGIEAkFeFVViFf/f/////8XcBQZNXpVVapUlEGkpEpPSqgUCTF2ZhFmbR3d3d0A0AAIFAk5dWaZVWu1u1u7BbVV7BQJMXVmEVVoHoiIiACA4AwUCTV4VVWIX///f/F39xd4FIkxcmYRIm4e5E53d3RHeBQJMXJmESJn/nRABHQEREgUCTFyZhEiZ/RwBAQER3fsFAk1eFVViFf3f3F393Ef+BQZOXJmmSJjFwfzcHM3c3gUCTV6VVWqWpqkr6SqoURAFIk1dndVZnraqqoKqqr6aBAJAXZmEWZgEA0A3d0AAAgUCTV6VVWqVDRESqrxqqGoFAk1eFVViFf/93d3/3/x9BQJOXJmmSJn93d/93AzMwgUCTV6VVWqUT9EGkqqSqSsFAu5QmaZImMTOXc3N3d/fBQJOXJmmSJnd3dzCHeI93AUGTl1ZplVZRtVVVXlXuu8FAk1eFVViFExFxdxF/93+BQJOXVmmVVru7VbXlBVDrgUCTl4VZmIUlIiIv/y8vIsFAy4ukSZqkf3GIfMiMz8zBQJNXhVVYhRP/ER93fxd3gUCTF1ZhFVbhiIiIiIjuDoFAk5dmaZZm7+5+d3cHd3dBQaNO9VVf9fWIVYhYIiUggUCTl1ZplVYBVbVeVbu7u8FAk5cmaZImN3d3B3eI/3iBQJOXpVmapRPxiBgRiIEYgUCTV4VVWIUfF3EX/3fxcYFAk1eFVViFE3cR/3f3d3eBQJOXVmmVVrtbXgW7XrtbgUCTl2ZplmZx53d37ufu7gFAk5eESZiEE/uL/7v7u7uBQJMXZmEWZgENAAAA0N3dgUCTl1ZplVYBUFVVtVtV64FAk5dWaZVWW7VVVQtVXlWBQJNXhVVYhX938f/xd/d3gUCTl1ZplVaxvl4FtVvr7oFBk1elVVqlE0Gqqq9KEaSBQJNXhVVYhf//9/9/d38XgUCTF1ZhFVbhjogICICOiIFAk1eFVViF/3cf////d3eBQJOXVmmVVgtVUFXlVbu7wUCTFyZhEiYJBEAEQHTkR0FAkxcmYRImAQAAQEBEROcBSZNXpVVapUOkRBqv+kSqgUCTl6VZmqUT+IiIiIiIGIFAk1elVVqlSaqqGqT6RKqBQJOXpVmapRMRiIGI+IHxgUCTV6VVWqWpRKqq/6pKoUFJk1f1VV/1iwVfAFUAJSKBQZOXpEmapI+IB4eHh/jIQUi7lPVZn/VJFERH9HdEd4FIkxcmYRImSXcE5wB0QHfBQJNXhVVYhXP/8f93d3HxwUCTV6VVWqUZEaoU9ESkqoFAk1dlVVZlSf9ERE9E//+BSaMOBmEQBldbvlVVUwAzgUmTVyVVUiUBAAAAAA4AAIFAk1eFVViFExERcXER8XeBQJNXhVVYhRMRdxF/d/9/gUCTV4VVWIVzf3d3d3d3f4FIkxcmYRImQUR3DndORECBAJAXZmEWZgHQAAAAAN3dgUCTFyZhEiZBRHBE5EBEAIFAkxdWYRVWAYiIiAiI4ADBQPNIlVVZlV2oiMhV/wOngUCzBWZhFmYP/xR5QxSqekFAu5SFWZiFC7BHe+7/v5WBQJPXNV3TNVlBV50SACIAwUi7FJVRGZUFo5hW6ijfqoFAo06FVViFSQoxFEevgWaBSJPXpE3apF8DSmep/8eawUCTF5VRGZVRUmarvb92NoFAywtGYRRGiTAWB9h/5e6BQMtLRmVURhn3wTgKY3+0gUCTV4VVWIURA6p3/5lY54FAk5dmaZZm/+oClViKJYiBQKMOlVEZlVPJqsqfrKeowUCTV0ZlVEZ7dZlbBVLoSoFAkxdWYRVWXQQFa+a8EhuBQLsUVmEVVpHpD4GehYVbgUCjTpVVWZVbR3a4td9yBoFAk1dGZVRGu7vSe+dLeAaBQPOIpVmapQNQZlNtad+qwUEDQLVVW7Vb+4C7ZXeIZsFA20JmZVZmswD4Se555IfBQLMFVmEVVjUTE1YFqlDvgUC7FFZhFVYBmIW7tziPV4FAo45GaZRGe1D7hHmXqZmBQKNOlVVZlXskv9eWYBExwUCjTpVVWZVDmX2HVPsiZ8FAu1RWZVVWiQok6oL3r4mBQKNOpVVapckI8T6mipmqwUDLi5RJmZQxhWOIiMr9ucFAk5eESZiEj2e5Ffu7jKeBQAOAtVmbtWUjcgf+OlclgUDLC0ZhFEZzVv0F7p1GZgFAswWlURqlM4cx6of+lqlBQJOXlVmZlR8EpXe/OxRegUCTV4VVWIUXqZ9Y29qbtIFAk1eFVViFm/fk+JlJ6h7BQKPOhE3YhPfKms6JWMGpgUDzSEZlVEYdMERGr6qWrIFAkxdWYRVWDXB/ir7MV4RBQKNOlVVZlY+Z+31nUEcSgUCzBaVRGqUX9sHdx3yqmYFAkxdWYRVW4U+3FWQQZGYBQLPFVm3VVpv7QNqq65uZwUgDwFZt1VZ/qZuIf0SqBIFA20JmZVZmnyTtefhJsQDBQKMORmEURjP6BIgTRTITgUDjUYVVWIWn6GkGiqzuv8FBow61URu1O78QiFenh3TBAJAXNmETNv9nh0ZBRRBBwUCTF1ZhFVZ9pvhI6y4QaoFAy4uVWZmVq/iohDSIQP2BQJOXZmmWZiuIWIoClffqgUCjDpVRGZWvqJ+sqspRyYFAkxeVURmVH7X6rWaka4eBQMtLVmVVVktH168nQHl5gUC7VEZlVEYxmlhXPzJnZYFA88hWbdVWR3anZ8DPx6mBQONRpVVapY2IV1aqB/85gUCzBWZhFmZvBETECvacQYFAk9c1XdM1JQASAFedVEHBQLNFVmVVVjFmaLvMvPy5gUC7VFZlVVZFe/gE7IuEioFAy0tmZVZmD2dSCvxS/3fBQLtUVmVVVo1kBFCnpk5nwUDbAlZhFVYvgQs0NoL9SIFAo06VVVmVexOrljSuib+BQJNXhVVYhUNDb5XFmKu5gUCTV5VVWZUJUfmIiFdXZMFAAwBmYRZmP/kXG1A32w2BQJOXlVmZlRfxrldJWBOpgUCTFzZhEzYJMTWRn8dEZMFAu1RWZVVWvSuLmZiOYN7BQNtClVVZlcGK+Mzjh6u5AUG7lJVZmZVJdGdGD2YU8oFIk1eFVViFm9sxAdgN+a2BQKNOlVVZlX+qVSZE8JmIwUCTV5VVWZVRZYlnu4dJX8FAu1RWZVVWW0A6UWdpeOzBQLsUVmEVVkc1GXhwFL93gUCTl4VZmIUtLMrCz+7ghIFAu5SFWZiFf0lQ9UfMdccBQJPXVV3VVXcwEDEQE72QAUCTF2ZhFmY/mXU5uwVT10FAk5d1WZd1QcjP/M//9P8BQJPXZV3WZQ83B3sANwADAUCzxXVd13X//3B3cHdwd7erq+sfLqUAAAAAAAAAAAA=")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_33kgk"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_kpp4c")
roughness = 0.5
uv1_offset = Vector3(0.91, 0.14, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_bee53"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_4jeef"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_33kgk"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_bee53")

[sub_resource type="BoxShape3D" id="BoxShape3D_npnwq"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_33kgk"]
_data = PackedByteArray("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")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_fygvl"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_33kgk")
roughness = 0.5
uv1_offset = Vector3(0.875, -0.015, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_d2lhk"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_3jg2o"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_fygvl"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_d2lhk")

[sub_resource type="BoxShape3D" id="BoxShape3D_kpp4c"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_fygvl"]
_data = PackedByteArray("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")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_bee53"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_fygvl")
roughness = 0.5
uv1_offset = Vector3(-0.425, 0.045, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_4icpn"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_ud6bv"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_bee53"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_4icpn")

[sub_resource type="BoxShape3D" id="BoxShape3D_33kgk"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="PortableCompressedTexture2D" id="PortableCompressedTexture2D_npnwq"]
_data = PackedByteArray("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")
size_override = Vector2(64, 64)
keep_compressed_buffer = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_kpp4c"]
resource_name = "PillarFrame"
render_priority = 1
depth_draw_mode = 2
albedo_texture = SubResource("PortableCompressedTexture2D_npnwq")
roughness = 0.5
uv1_offset = Vector3(0.985, 0.315, 0)

[sub_resource type="ArrayMesh" id="ArrayMesh_nhaao"]
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAAAAIAAwAFAAEABAACAAYAAwAHAAUABwAEAAYABwAGAAUA"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("//8AAP//AAAAAP////8AAP///////wAAAAAAAP//AAD//wAAAAAAAAAA//8AAAAA/////wAAAAAAAAAAAAAAAA==")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"format": 34896613377,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAAAAAAAP////8AAAAA//8AAAAA////////AAAAAAAAAAAAAP//AAD//wAA//8AAAAAAAAAAAAA//8AAA==")
}]
blend_shape_mode = 0

[sub_resource type="ArrayMesh" id="ArrayMesh_oeqx6"]
resource_name = "pillar1_Cube"
_surfaces = [{
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("///6/v+/AAD//wAA/7/6/v+/+v7/fwAA/78AAP9/+v7///r+/78AAP//AAD/v/r+/7/6/v9/AAD/vwAA/3/6/g=="),
"format": 34896613399,
"index_count": 24,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUACAAJAAoACAALAAkADAANAA4ADAAPAA0A"),
"material": SubResource("StandardMaterial3D_kbiir"),
"name": "PillarSide",
"primitive": 3,
"uv_scale": Vector4(2, 2.016, 0, 0),
"vertex_count": 16,
"vertex_data": PackedByteArray("//8AAP//AIAAAP////8AgP///////wCAAAAAAP//AIAAAAAA////vwAA//8AAP+/AAD//////78AAAAAAAD/v///AAAAAP+//////////7//////AAD/v///AAD///+/AAAAAAAA////////AAD//wAA//8AAP////8AAAAA////f////3////9/////f////3////9/////f////3////9/AAD/fwAA/38AAP9/AAD/f////3////9/////f///")
}, {
"aabb": AABB(-0.155, -1.28, -0.155, 0.31, 2.56, 0.31),
"attribute_data": PackedByteArray("4FW5leCVuVXglbmV4FW5Vf8f/3//X/8//1//f/8f/z8="),
"format": 34896613399,
"index_count": 12,
"index_data": PackedByteArray("AAABAAIAAAADAAEABAAFAAYABAAHAAUA"),
"material": SubResource("StandardMaterial3D_kpp4c"),
"name": "PillarFrame",
"primitive": 3,
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 8,
"vertex_data": PackedByteArray("/////wAA//8AAP///////wAA//8AAP////////////8AAAAAAAD/v///AAD///+///8AAAAA/78AAAAA////v/9//7//f/+//3//v/9//78AAP9/AAD/fwAA/38AAP9/")
}]
blend_shape_mode = 0
shadow_mesh = SubResource("ArrayMesh_nhaao")

[sub_resource type="BoxShape3D" id="BoxShape3D_fygvl"]
size = Vector3(0.31, 2.56, 0.31)

[sub_resource type="Animation" id="Animation_a4yma"]
resource_name = "Open"
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimatableBody3D:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 1),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector3(0, 0, 0), Vector3(0, 2.559, 0)]
}

[sub_resource type="Animation" id="Animation_8paui"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("AnimatableBody3D:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(0, 0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8paui"]
_data = {
&"Open": SubResource("Animation_a4yma"),
&"RESET": SubResource("Animation_8paui")
}

[node name="MovingBars1" type="Node3D" groups=["door"]]
script = ExtResource("1_nhaao")
open_only = true
open_sound = ExtResource("2_oeqx6")
can_interrupt = false
allow_front = false
allow_back = false

[node name="AnimatableBody3D" type="AnimatableBody3D" parent="."]
collision_layer = 4
collision_mask = 0

[node name="MeshInstance3D" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.4, -0.00782776, 0)
mesh = SubResource("ArrayMesh_fygvl")

[node name="CollisionShape3D" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 1.4, -0.00782776, 0)
shape = SubResource("BoxShape3D_a4yma")

[node name="MeshInstance3D2" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.799999, -0.00782776, 0)
mesh = SubResource("ArrayMesh_kpp4c")

[node name="CollisionShape3D2" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.799999, -0.00782776, 0)
shape = SubResource("BoxShape3D_oeqx6")

[node name="MeshInstance3D3" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.200001, -0.00782776, 0)
mesh = SubResource("ArrayMesh_4jeef")

[node name="CollisionShape3D3" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0.200001, -0.00782776, 0)
shape = SubResource("BoxShape3D_npnwq")

[node name="MeshInstance3D4" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.400002, -0.00782776, 0)
mesh = SubResource("ArrayMesh_3jg2o")

[node name="CollisionShape3D4" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.400002, -0.00782776, 0)
shape = SubResource("BoxShape3D_kpp4c")

[node name="MeshInstance3D5" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, -0.00782776, 0)
mesh = SubResource("ArrayMesh_ud6bv")

[node name="CollisionShape3D5" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1, -0.00782776, 0)
shape = SubResource("BoxShape3D_33kgk")

[node name="MeshInstance3D6" type="MeshInstance3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.6, -0.00782776, 0)
mesh = SubResource("ArrayMesh_oeqx6")

[node name="CollisionShape3D6" type="CollisionShape3D" parent="AnimatableBody3D"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.6, -0.00782776, 0)
shape = SubResource("BoxShape3D_fygvl")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_8paui")
}
