[gd_scene load_steps=4 format=3 uid="uid://bxcoqwwtje00u"]

[ext_resource type="Texture2D" uid="uid://bh5sixu5c2uoc" path="res://images/objects/glasses/1187A0.png" id="1_3d6u2"]
[ext_resource type="Script" uid="uid://0lax4sx4ont5" path="res://scenes/objects/break_glass.gd" id="1_jsu7e"]

[sub_resource type="BoxShape3D" id="BoxShape3D_px8vl"]
size = Vector3(0.0771484, 0.208984, 0.0771484)

[node name="Glass12" type="StaticBody3D"]
collision_layer = 4
collision_mask = 7
script = ExtResource("1_jsu7e")

[node name="Sprite3D" type="Sprite3D" parent="."]
billboard = 2
shaded = true
texture_filter = 2
texture = ExtResource("1_3d6u2")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_px8vl")
