[gd_scene load_steps=5 format=3 uid="uid://swf63peiq0st"]

[ext_resource type="Script" uid="uid://diballdndevut" path="res://scenes/items/health.gd" id="1_bbw8y"]
[ext_resource type="Texture2D" uid="uid://cgsk0d7587b1i" path="res://images/items/SSHIA0.png" id="1_k1irs"]
[ext_resource type="AudioStream" uid="uid://dv86w10agao07" path="res://sounds/sfx/DSWPNUP.wav" id="2_ytmuv"]

[sub_resource type="BoxShape3D" id="BoxShape3D_7y171"]
size = Vector3(0.522461, 0.220703, 0.501953)

[node name="Sushi" type="Area3D" groups=["item"]]
collision_layer = 8
script = ExtResource("1_bbw8y")
event_string = "Ate sushi!"
pickup_sound = ExtResource("2_ytmuv")
heal_value = 25

[node name="Sprite3D" type="Sprite3D" parent="."]
transform = Transform3D(0.7, 0, 0, 0, 0.7, 0, 0, 0, 0.7, 0, 0, 0)
billboard = 2
shaded = true
render_priority = 1
texture = ExtResource("1_k1irs")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("BoxShape3D_7y171")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
